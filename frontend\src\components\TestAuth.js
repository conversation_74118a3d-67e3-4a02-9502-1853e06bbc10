import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

const TestAuth = () => {
    const { user, logout } = useAuth();

    return (
        <div className="container mt-5">
            <div className="row justify-content-center">
                <div className="col-md-6">
                    <div className="card">
                        <div className="card-body">
                            <h2 className="text-center mb-4">Authentication Test</h2>
                            
                            {user ? (
                                <div>
                                    <h4>Welcome, {user.fullName}!</h4>
                                    <p>Your details:</p>
                                    <ul className="list-group mb-3">
                                        <li className="list-group-item">Username: {user.username}</li>
                                        <li className="list-group-item">Email: {user.email}</li>
                                    </ul>
                                    <button 
                                        onClick={logout}
                                        className="btn btn-danger w-100"
                                    >
                                        Logout
                                    </button>
                                    <Link to="/dashboard" className="btn btn-primary w-100 mt-2">
                                        Go to Dashboard
                                    </Link>
                                </div>
                            ) : (
                                <div>
                                    <p className="text-center">You are not logged in.</p>
                                    <div className="d-grid gap-2">
                                        <Link to="/login" className="btn btn-primary">
                                            Login
                                        </Link>
                                        <Link to="/register" className="btn btn-secondary">
                                            Register
                                        </Link>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default TestAuth;
