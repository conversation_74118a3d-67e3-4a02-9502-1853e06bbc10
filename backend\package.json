{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "nodemon index.js"}, "author": "", "license": "ISC", "devDependencies": {"nodemon": "^3.1.10"}, "dependencies": {"axios": "^1.11.0", "body-parser": "^2.2.0", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "mongoose": "^8.18.0", "passport": "^0.7.0", "passport-local": "^1.0.0", "passport-local-mongoose": "^8.0.0"}}