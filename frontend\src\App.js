import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Dashboard from './dashboard/Dashboard';
import './App.css';

function App() {
  return (
    <Router>
      <div className="App">
        <Routes>
          function App() {
    <Router>
      <div className="App">
        <Routes>
          <Route path="/signup" element={<Signup />} />
        </Routes>
      </div>
    </Router>
  );
}
          </Routes>
        </div>
      </Router>
    </AuthProvider>
  );
}
    </div>
  );
}

export default App;
