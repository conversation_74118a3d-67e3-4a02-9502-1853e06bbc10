<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auth Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        input {
            width: 100%;
            padding: 8px;
            margin-top: 5px;
        }
        button {
            padding: 10px 15px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .response {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>Authentication Test Page</h1>

    <div>
        <h2>Register</h2>
        <div class="form-group">
            <label>Username:</label>
            <input type="text" id="reg-username">
        </div>
        <div class="form-group">
            <label>Email:</label>
            <input type="email" id="reg-email">
        </div>
        <div class="form-group">
            <label>Password:</label>
            <input type="password" id="reg-password">
        </div>
        <div class="form-group">
            <label>Full Name:</label>
            <input type="text" id="reg-fullname">
        </div>
        <button onclick="register()">Register</button>
    </div>

    <hr>

    <div>
        <h2>Login</h2>
        <div class="form-group">
            <label>Username:</label>
            <input type="text" id="login-username">
        </div>
        <div class="form-group">
            <label>Password:</label>
            <input type="password" id="login-password">
        </div>
        <button onclick="login()">Login</button>
    </div>

    <hr>

    <div>
        <h2>Get Current User</h2>
        <button onclick="getCurrentUser()">Get User Info</button>
    </div>

    <hr>

    <div>
        <h2>Logout</h2>
        <button onclick="logout()">Logout</button>
    </div>

    <div class="response">
        <h3>Response:</h3>
        <pre id="response"></pre>
    </div>

    <script>
        const API_URL = 'http://localhost:8000/api/auth';

        async function showResponse(response) {
            const responseElement = document.getElementById('response');
            try {
                const data = await response.json();
                console.log('Response:', data); // Debug log
                responseElement.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                console.error('Error parsing response:', error); // Debug log
                responseElement.textContent = `Error: ${error.message}`;
            }
        }

        async function register() {
            try {
                const userData = {
                    username: document.getElementById('reg-username').value,
                    email: document.getElementById('reg-email').value,
                    password: document.getElementById('reg-password').value,
                    fullName: document.getElementById('reg-fullname').value
                };
                
                console.log('Sending registration data:', userData); // Debug log

                const response = await fetch(`${API_URL}/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify(userData)
                });

                console.log('Response status:', response.status); // Debug log
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                await showResponse(response);
            } catch (error) {
                console.error('Registration error:', error); // Debug log
                document.getElementById('response').textContent = `Error: ${error.message}`;
            }
        }

        async function login() {
            const response = await fetch(`${API_URL}/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include',
                body: JSON.stringify({
                    username: document.getElementById('login-username').value,
                    password: document.getElementById('login-password').value
                })
            });
            showResponse(response);
        }

        async function getCurrentUser() {
            const response = await fetch(`${API_URL}/me`, {
                credentials: 'include'
            });
            showResponse(response);
        }

        async function logout() {
            const response = await fetch(`${API_URL}/logout`, {
                method: 'POST',
                credentials: 'include'
            });
            showResponse(response);
        }
    </script>
</body>
</html>
